<style>
    /* Override any conflicting styles for pricing section */
    #pricing .pricing-card {
        background-color: #f0fdf4 !important;
    }
    #pricing .pricing-card:nth-child(even) {
        background-color: #dcfce7 !important;
    }
    #pricing .billing-toggle {
        background-color: #bbf7d0 !important;
    }
    #pricing .billing-option.active-tab {
        background-color: #f0fdf4 !important;
        color: #166534 !important;
        border-color: #86efac !important;
    }
    #pricing .billing-option:not(.active-tab) {
        color: #15803d !important;
    }
    #pricing .billing-option:not(.active-tab):hover {
        background-color: #dcfce7 !important;
        color: #166534 !important;
    }
    /* Ensure buttons are visible */
    #pricing .pricing-card a[href*="register"] {
        background-color: #10b981 !important;
        color: white !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
    #pricing .pricing-card:nth-child(even) a[href*="register"] {
        background-color: #059669 !important;
    }
</style>

<section id="pricing" class="relative py-20 bg-green-50">
    <div class="relative px-10 mx-auto max-w-7xl xl:px-16">
        <div class="max-w-3xl mx-auto mb-12 text-center lg:mb-20">
            <span class="text-sm font-semibold text-green-500"><?php echo e(__('wpbox.our_subscription_plans')); ?></span>
            <h2 class="mt-3 mb-10 text-4xl font-bold font-heading"><?php echo e(__('wpbox.simple_flexible_pricing')); ?></h2>
            <p class="mb-10 text-xl text-gray-500"><?php echo e(__('wpbox.subscription_plans_description')); ?></p>
            
            <!-- Billing Cycle Toggle -->
            <div class="flex justify-center items-center mb-16">
                <div class="billing-toggle flex p-1 bg-green-200 rounded-lg shadow-md">
                    <button id="monthlyToggle" class="billing-option px-6 py-2 text-sm font-medium rounded-md focus:outline-none bg-green-50 shadow-md text-green-800 active-tab border border-green-300"><?php echo e(__('Monthly')); ?></button>
                    <button id="quarterlyToggle" class="billing-option px-6 py-2 text-sm font-medium rounded-md focus:outline-none text-green-700 hover:text-green-800 hover:bg-green-100 transition-all duration-200"><?php echo e(__('Yearly')); ?></button>
                </div>
            </div>
        </div>

        <!-- Monthly Plans Section -->
        <div id="monthlyPlans" class="billing-content">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $keyp => $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($plan['period'] == 1): ?>
                        <div class="pricing-card rounded-xl overflow-hidden flex flex-col h-full transition-all duration-300 hover:shadow-xl border-2 shadow-md" style="background-color: <?php echo e($keyp % 2 == 0 ? '#f0fdf4' : '#dcfce7'); ?>; border-color: <?php echo e($keyp % 2 == 0 ? '#bbf7d0' : '#86efac'); ?>;">
                            <style>
                                .pricing-card:hover {
                                    border-color: <?php echo e($keyp % 2 == 0 ? '#86efac' : '#4ade80'); ?> !important;
                                }
                            </style>
                            <div class="p-6">
                                <div class="mb-6">
                                    <h3 class="text-2xl font-bold font-heading" style="color: #166534 !important;"><?php echo e($plan->name); ?></h3>
                                    <div class="description-container mt-3 text-gray-500 relative h-12 overflow-hidden">
                                        <p class="plan-description"><?php echo e($plan->description); ?></p>
                                        <div class="expand-btn absolute bottom-0 right-0 px-1 <?php echo e($keyp % 2 == 0 ? 'bg-green-50' : 'bg-green-100'); ?> text-green-600 cursor-pointer" onclick="expandDescription(this)">
                                            <svg class="chevron-down w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                            </svg>
                                            <svg class="chevron-up w-5 h-5 hidden" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="flex flex-col justify-center items-center mb-6">
                                    <?php if($plan->price == 0): ?>
                                        <p class="text-xl font-bold italic" style="color: #15803d !important;"><?php echo e(__('Contact Sales')); ?></p>
                                        <p class="text-sm mt-1" style="color: #6b7280 !important;"><?php echo e(__('Custom Pricing')); ?></p>
                                    <?php else: ?>
                                    <div class="flex justify-center items-center">
                                        <span class="self-start inline-block mr-1 text-xl font-bold" style="color: #15803d !important;"><?php echo e(config('money')[strtoupper(config('settings.cashier_currency'))]['symbol']); ?></span>
                                        <p class="text-4xl font-bold font-heading" style="color: #166534 !important;"><?php echo e(number_format($plan->price)); ?><span class="ml-1 text-sm text-gray-600">/ <?php echo e(__('month')); ?></span></p>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="mb-8">
                                    <?php
                                        $features = explode(",", $plan['features']);
                                        $currentHeading = null;
                                        $hasStartedList = false;
                                    ?>

                                    <?php $__currentLoopData = $features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            $feature = trim($feature);
                                            // Check if this is a heading (format: ##HEADING##)
                                            if (preg_match('/^##(.+)##$/', $feature, $matches)) {
                                                $currentHeading = trim($matches[1]);
                                                if ($hasStartedList) {
                                                    echo '</ul>';
                                                    $hasStartedList = false;
                                                }
                                                continue;
                                            }
                                        ?>

                                        <?php if($currentHeading && !$hasStartedList): ?>
                                            <h4 class="text-sm font-bold mb-2 pb-1 border-b <?php echo e($index > 0 ? 'mt-4' : ''); ?>" style="color: #166534 !important; border-color: #86efac !important;"><?php echo e($currentHeading); ?></h4>
                                            <ul class="space-y-2">
                                            <?php
                                                $hasStartedList = true;
                                                $currentHeading = null;
                                            ?>
                                        <?php elseif(!$hasStartedList): ?>
                                            <ul class="space-y-2">
                                            <?php $hasStartedList = true; ?>
                                        <?php endif; ?>

                                        <?php if(!empty($feature)): ?>
                                            <li class="flex items-center">
                                                <svg class="mr-2 <?php echo e($keyp % 2 == 0 ? 'text-green-500' : 'text-green-600'); ?> w-5 h-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                </svg>
                                                <p class="text-sm font-medium"><?php echo e($feature); ?></p>
                                            </li>
                                        <?php endif; ?>

                                        <?php if($loop->last && $hasStartedList): ?>
                                            </ul>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                            
                            <div class="mt-auto p-6 bg-white border-t border-gray-100">
                                <?php if(!config('settings.disable_registration_page',false)): ?>
                                <a class="block py-3 px-6 text-sm font-medium leading-normal text-center transition duration-200 rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 transform hover:scale-105"
                                   style="background-color: <?php echo e($keyp % 2 == 0 ? '#10b981' : '#059669'); ?>; color: white; text-decoration: none;"
                                   onmouseover="this.style.backgroundColor='<?php echo e($keyp % 2 == 0 ? '#059669' : '#047857'); ?>'"
                                   onmouseout="this.style.backgroundColor='<?php echo e($keyp % 2 == 0 ? '#10b981' : '#059669'); ?>'"
                                   href="<?php echo e(route('register')); ?>"><?php echo e(__('wpbox.start_now')); ?></a>
                                <?php endif; ?>
                                <p class="mt-3 text-xs text-center text-gray-500"><?php echo e(__('wpbox.no_contracts')); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        
        <!-- Yearly Plans Section -->
        <div id="quarterlyPlans" class="billing-content hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $keyp => $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($plan['period'] != 1): ?>
                        <div class="pricing-card rounded-xl overflow-hidden flex flex-col h-full transition-all duration-300 hover:shadow-xl border-2 shadow-md" style="background-color: <?php echo e($keyp % 2 == 0 ? '#f0fdf4' : '#dcfce7'); ?>; border-color: <?php echo e($keyp % 2 == 0 ? '#bbf7d0' : '#86efac'); ?>;">
                            <style>
                                .pricing-card:hover {
                                    border-color: <?php echo e($keyp % 2 == 0 ? '#86efac' : '#4ade80'); ?> !important;
                                }
                            </style>
                            <div class="p-6">
                                <div class="mb-6">
                                    <h3 class="text-2xl font-bold font-heading" style="color: #166534 !important;"><?php echo e($plan->name); ?></h3>
                                    <div class="description-container mt-3 text-gray-500 relative h-12 overflow-hidden">
                                        <p class="plan-description"><?php echo e($plan->description); ?></p>
                                        <div class="expand-btn absolute bottom-0 right-0 px-1 <?php echo e($keyp % 2 == 0 ? 'bg-green-50' : 'bg-green-100'); ?> text-green-600 cursor-pointer" onclick="expandDescription(this)">
                                            <svg class="chevron-down w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                            </svg>
                                            <svg class="chevron-up w-5 h-5 hidden" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="flex flex-col justify-center items-center mb-6">
                                    <?php if($plan->price == 0): ?>
                                        <p class="text-xl font-bold italic" style="color: #15803d !important;"><?php echo e(__('Contact Sales')); ?></p>
                                        <p class="text-sm mt-1" style="color: #6b7280 !important;"><?php echo e(__('Custom Pricing')); ?></p>
                                    <?php else: ?>
                                        <div class="flex justify-center items-center">
                                            <span class="self-start inline-block mr-1 text-xl font-bold" style="color: #15803d !important;"><?php echo e(config('money')[strtoupper(config('settings.cashier_currency'))]['symbol']); ?></span>
                                            <p class="text-4xl font-bold font-heading" style="color: #166534 !important;"><?php echo e(number_format($plan->price)); ?><span class="ml-1 text-sm text-gray-600">/ <?php echo e(__('year')); ?></span></p>
                                        </div>
                                        <?php
                                            // Find corresponding monthly plan with same name
                                            $monthlyPlan = collect($plans)->where('name', $plan->name)->where('period', 1)->first();
                                            if ($monthlyPlan) {
                                                $monthlyYearlyTotal = $monthlyPlan->price * 12;
                                                $savings = $monthlyYearlyTotal - $plan->price;
                                            }
                                        ?>
                                        <?php if(isset($monthlyPlan) && $savings > 0): ?>
                                            <p class="text-sm text-green-600 font-medium mt-2">
                                                Save <?php echo e(config('money')[strtoupper(config('settings.cashier_currency'))]['symbol']); ?><?php echo e(number_format($savings, 2)); ?> annually
                                            </p>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="mb-8">
                                    <?php
                                        $features = explode(",", $plan['features']);
                                        $currentHeading = null;
                                        $hasStartedList = false;
                                    ?>

                                    <?php $__currentLoopData = $features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            $feature = trim($feature);
                                            // Check if this is a heading (format: ##HEADING##)
                                            if (preg_match('/^##(.+)##$/', $feature, $matches)) {
                                                $currentHeading = trim($matches[1]);
                                                if ($hasStartedList) {
                                                    echo '</ul>';
                                                    $hasStartedList = false;
                                                }
                                                continue;
                                            }
                                        ?>

                                        <?php if($currentHeading && !$hasStartedList): ?>
                                            <h4 class="text-sm font-bold mb-2 pb-1 border-b <?php echo e($index > 0 ? 'mt-4' : ''); ?>" style="color: #166534 !important; border-color: #86efac !important;"><?php echo e($currentHeading); ?></h4>
                                            <ul class="space-y-2">
                                            <?php
                                                $hasStartedList = true;
                                                $currentHeading = null;
                                            ?>
                                        <?php elseif(!$hasStartedList): ?>
                                            <ul class="space-y-2">
                                            <?php $hasStartedList = true; ?>
                                        <?php endif; ?>

                                        <?php if(!empty($feature)): ?>
                                            <li class="flex items-center">
                                                <svg class="mr-2 <?php echo e($keyp % 2 == 0 ? 'text-green-500' : 'text-green-600'); ?> w-5 h-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                </svg>
                                                <p class="text-sm font-medium"><?php echo e($feature); ?></p>
                                            </li>
                                        <?php endif; ?>

                                        <?php if($loop->last && $hasStartedList): ?>
                                            </ul>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                            
                            <div class="mt-auto p-6 bg-white border-t border-gray-100">
                                <?php if(!config('settings.disable_registration_page',false)): ?>
                                <a class="block py-3 px-6 text-sm font-medium leading-normal text-center transition duration-200 rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 transform hover:scale-105"
                                   style="background-color: <?php echo e($keyp % 2 == 0 ? '#10b981' : '#059669'); ?>; color: white; text-decoration: none;"
                                   onmouseover="this.style.backgroundColor='<?php echo e($keyp % 2 == 0 ? '#059669' : '#047857'); ?>'"
                                   onmouseout="this.style.backgroundColor='<?php echo e($keyp % 2 == 0 ? '#10b981' : '#059669'); ?>'"
                                   href="<?php echo e(route('register')); ?>"><?php echo e(__('wpbox.start_now')); ?></a>
                                <?php endif; ?>
                                <p class="mt-3 text-xs text-center text-gray-500"><?php echo e(__('wpbox.no_contracts')); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>

    </div>
    
    <!-- Toggle functionality with JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const monthlyToggle = document.getElementById('monthlyToggle');
            const quarterlyToggle = document.getElementById('quarterlyToggle');
            const monthlyPlans = document.getElementById('monthlyPlans');
            const quarterlyPlans = document.getElementById('quarterlyPlans');
            
            // Function to initialize description containers
            function initDescriptionContainers(section) {
                const containers = section.querySelectorAll('.description-container');
                containers.forEach(container => {
                    const paragraph = container.querySelector('.plan-description');
                    if (paragraph.scrollHeight > container.clientHeight) {
                        container.querySelector('.expand-btn').style.display = 'inline-block';
                    } else {
                        container.querySelector('.expand-btn').style.display = 'none';
                    }
                });
            }
            
            // Initialize both sections on load
            initDescriptionContainers(monthlyPlans);
            
            // Toggle between billing cycles
            monthlyToggle.addEventListener('click', function() {
                monthlyPlans.classList.remove('hidden');
                quarterlyPlans.classList.add('hidden');

                // Update monthly toggle to active state
                monthlyToggle.style.backgroundColor = '#f0fdf4';
                monthlyToggle.style.color = '#166534';
                monthlyToggle.style.borderColor = '#86efac';
                monthlyToggle.classList.add('shadow-md', 'active-tab', 'border');

                // Update yearly toggle to inactive state
                quarterlyToggle.style.backgroundColor = 'transparent';
                quarterlyToggle.style.color = '#15803d';
                quarterlyToggle.style.borderColor = 'transparent';
                quarterlyToggle.classList.remove('shadow-md', 'active-tab', 'border');

                // Ensure descriptions are properly initialized
                initDescriptionContainers(monthlyPlans);
            });

            quarterlyToggle.addEventListener('click', function() {
                monthlyPlans.classList.add('hidden');
                quarterlyPlans.classList.remove('hidden');

                // Update yearly toggle to active state
                quarterlyToggle.style.backgroundColor = '#f0fdf4';
                quarterlyToggle.style.color = '#166534';
                quarterlyToggle.style.borderColor = '#86efac';
                quarterlyToggle.classList.add('shadow-md', 'active-tab', 'border');

                // Update monthly toggle to inactive state
                monthlyToggle.style.backgroundColor = 'transparent';
                monthlyToggle.style.color = '#15803d';
                monthlyToggle.style.borderColor = 'transparent';
                monthlyToggle.classList.remove('shadow-md', 'active-tab', 'border');

                // Initialize quarterly descriptions on first toggle
                initDescriptionContainers(quarterlyPlans);
            });
            
            // Initialize monthly section descriptions (visible by default)
            const descContainers = document.querySelectorAll('.description-container');
            descContainers.forEach(container => {
                const paragraph = container.querySelector('.plan-description');
                if (paragraph.scrollHeight > container.clientHeight) {
                    container.querySelector('.expand-btn').style.display = 'inline-block';
                } else {
                    container.querySelector('.expand-btn').style.display = 'none';
                }
            });
        });
        
        // Function to expand/collapse description
        function expandDescription(button) {
            const container = button.parentElement;
            const paragraph = container.querySelector('.plan-description');
            const chevronDown = button.querySelector('.chevron-down');
            const chevronUp = button.querySelector('.chevron-up');
            const isExpanded = paragraph.getAttribute('data-expanded') === 'true';
            
            if (isExpanded) {
                // Collapse
                container.style.height = '3rem'; // h-12 is 3rem
                paragraph.setAttribute('data-expanded', 'false');
                paragraph.style.webkitLineClamp = '2';
                chevronDown.classList.remove('hidden');
                chevronUp.classList.add('hidden');
            } else {
                // Expand
                const fullHeight = paragraph.scrollHeight + 10; // Add some padding
                container.style.height = fullHeight + 'px';
                paragraph.setAttribute('data-expanded', 'true');
                paragraph.style.webkitLineClamp = 'unset';
                chevronDown.classList.add('hidden');
                chevronUp.classList.remove('hidden');
            }
        }
    </script>
    
    <style>
        /* Style for description containers */
        .description-container {
            position: relative;
            transition: height 0.2s ease;
        }
        
        .plan-description {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .expand-btn {
            display: none;
            z-index: 10;
        }
    </style>
</section><?php /**PATH C:\xampp\htdocs\zaptra\modules\Wpboxlanding\Providers/../Resources/views/landing/partials/pricing.blade.php ENDPATH**/ ?>