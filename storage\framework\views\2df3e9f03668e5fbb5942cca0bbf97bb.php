
<?php $__env->startSection('thead'); ?>
    <th><?php echo e(__('Name')); ?></th>
    <th><?php echo e(__('crud.actions')); ?></th>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('tbody'); ?>
    <?php $__currentLoopData = $setup['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <tr>
            <td><?php echo e($item->name); ?></td>
            <td>
                <!-- EDIT -->
                <a href="<?php echo e(route('flowisebots.edit',['bot'=>$item->id])); ?>" class="btn btn-primary btn-sm">
                    <i class="ni ni-ruler-pencil"></i>
                </a>

                <!-- Delete -->
                <a href="<?php echo e(route('flowisebots.delete',['bot'=>$item->id])); ?>" class="btn btn-danger btn-sm">
                    <i class="ni ni ni-fat-remove"></i>
                </a>
            </td>
        </tr> 
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('general.index', $setup, \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\zaptra\modules\Flowiseai\Providers/../Resources/views/index.blade.php ENDPATH**/ ?>