


<?php $__env->startSection('content'); ?>
    <div class="header  pb-8 pt-5 pt-md-8">
    </div>


    <div class="container-fluid mt--7">
        <div class="row">
            <div class="col-12">
                <?php echo $__env->make('partials.flash', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-header border-0">
                        <div class="row align-items-center">
                            <div class="col-8">
                                <h3 class="mb-0"><?php echo e(__("Whatsapp Web Widget creator")); ?></h3>
                  
   
                                
                            </div>
                            
                               
                        </div>
                       
                    </div>

                   

                   
                       <div class="card-body">
                            <form action="<?php echo e(route('embedwhatsapp.store')); ?>" method="POST" enctype="multipart/form-data">
                                <?php echo csrf_field(); ?>
                                <!-- Header image partials.images -->
                                <?php echo $__env->make('partials.images',['image'=>['name'=>'logo','label'=>__('Logo'),'value'=>$widget['logo'],'style'=>'width: 200px;']], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                                <!-- Phone number -->
                                <?php echo $__env->make('partials.input',['class'=>'col-md-12','name'=>"WhatsApp Phone number",'id'=>'phone_number','type'=>'text','placeholder'=>"Phone number",'required'=>true, 'value'=>$widget['phone_number']], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                                <!-- Header text -->
                                <?php echo $__env->make('partials.input',['class'=>'col-md-12','name'=>"Header text",'id'=>'header_text','type'=>'text','placeholder'=>"Header text",'required'=>true, 'value'=>$widget['header_text']], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                                <!-- Header subtext -->
                                <?php echo $__env->make('partials.input',['class'=>'col-md-12','name'=>"Header subtext",'id'=>'header_subtext','type'=>'text','placeholder'=>"Header subtext",'required'=>true, 'value'=>$widget['header_subtext']], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                                <!-- Text Area -->
                                <?php echo $__env->make('partials.textarea',['class'=>'col-md-12','name'=>"Widget text",'id'=>'widget_text','placeholder'=>"Widget text",'required'=>true, 'value'=>$widget['widget_text']], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                                <!-- Button text -->
                                <?php echo $__env->make('partials.input',['class'=>'col-md-12','name'=>"Button text",'id'=>'button_text','type'=>'text','placeholder'=>"Button text",'required'=>true, 'value'=>$widget['button_text']], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                                <!-- Widget type -->
                                <?php echo $__env->make('partials.select',['class'=>'col-md-12','name'=>"Widget type",'id'=>'widget_type','data'=>['1'=>'With start chat button','2'=>'With input field'],'required'=>true, 'value'=>$widget['widget_type']], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                                <!-- Input field placeholder -->
                                <?php echo $__env->make('partials.input',['class'=>'col-md-12','name'=>"Input field placeholder",'id'=>'input_field_placeholder','type'=>'text','placeholder'=>"Input field placeholder",'required'=>true, 'value'=>$widget['input_field_placeholder']], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                                <!-- Button color -->
                                <?php echo $__env->make('partials.colorpicker',['class'=>'col-md-12','name'=>"Button color",'id'=>'button_color','placeholder'=>"Button color","value"=>"#14c656",'required'=>true, 'value'=>$widget['button_color']], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                                <!-- Header color -->
                                <?php echo $__env->make('partials.colorpicker',['class'=>'col-md-12','name'=>"Header color",'id'=>'header_color','placeholder'=>"Header color","value"=>"#006654",'required'=>true, 'value'=>$widget['header_color']], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                <div class="form-group">
                                    <button type="submit" class="btn btn-success ml-3 mt-2" ><?php echo e(__('Save')); ?></button>
                                </div>
                                
                            </form>
                        </div>
                   
                    
     
         


                </div>
            </div>
            <?php if(isset($widget['url'])): ?>
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-header border-0">
                        <div class="row align-items-center">
                            <div class="col-8">
                                <h3 class="mb-0"><?php echo e(__("Embedde widget code")); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="form-group row">
                           <div class="col-md-12">
                                <textarea class="form-control" id="code" rows="2" readonly>
<script src="<?php echo e($widget['url']); ?>"></script>
<div id="embed-whatsapp-chat"></div>
                                </textarea>
                                


                                <!-- copy code button -->
                                <button class="btn btn-primary mt-2" onclick="copyToClipboard('code');"><?php echo e(__('Copy code')); ?></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <script>
            function copyToClipboard(elementId) {
                var aux = document.createElement("input");
                aux.setAttribute("value", document.getElementById(elementId).value);
                document.body.appendChild(aux);
                aux.select();
                document.execCommand("copy");
                document.body.removeChild(aux);
                $.notify("Widget code copied to clipboard",{
                    position: "top right",
                    className: 'success',
                });
                //js.notify('Copyed to clipboaar')
            }
        </script>


        <?php echo $__env->make('layouts.footers.auth', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php if(isset($widget['url'])): ?>
            <script src="<?php echo $widget['url'];  ?>"></script>
        <?php endif; ?>
        
        <div id="embed-whatsapp-chat"></div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', ['title' =>  __("Whatsapp Web Widget creator") ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\zaptra\modules\Embedwhatsapp\Providers/../Resources/views/edit.blade.php ENDPATH**/ ?>