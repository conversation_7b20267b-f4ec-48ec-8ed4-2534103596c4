

<?php $__env->startSection('content'); ?>
    <div class="header pb-8 pt-5 pt-md-8">
        <div class="container-fluid">
            <div class="header-body">
                <h1 class="mb-3 mt--3">💰 <?php echo e(__('Pricing plans')); ?></h1>
              <div class="row align-items-center pt-2">
              </div>
            </div>
        </div>
    </div>

    <div class="container-fluid mt--7">
        <div class="row">
            <div class="col">
                <div class="card shadow">
                    <div class="card-header border-0">
                        <div class="row align-items-center">
                            <div class="col-8">
                                
                            </div>
                            <div class="col-4 text-right">
                                <a href="<?php echo e(route('plans.create')); ?>" class="btn btn-sm btn-primary"><?php echo e(__('Add plan')); ?></a>
                            </div>
                        </div>
                    </div>

                    <div class="col-12">
                        <?php echo $__env->make('partials.flash', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                    <?php if(count($plans)): ?>
                    <div class="table-responsive">
                        <table class="table align-items-center table-flush">
                            <thead class="thead-light">
                                <tr>
                                    <th scope="col"><?php echo e(__('Name')); ?></th>
                                    <th scope="col"><?php echo e(__('Price')); ?></th>
                                    <?php if(config('settings.enable_credits')): ?>
                                        <th scope="col"><?php echo e(__('Credit amount')); ?></th>
                                    <?php endif; ?>
                                    <th scope="col"><?php echo e(__('Period')); ?></th>
                                    <th scope="col"></th>
                                </tr>
                            </thead>
                            <tbody>
                            <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><a href="<?php echo e(route('plans.edit', $plan)); ?>"><?php echo e($plan->name); ?> </a></td>
                                    <td><?php echo money($plan->price, config('settings.site_currency','usd'),config('settings.site_do_currency',true)); ?>/<?php echo e($plan->period==1?__('m'):__('y')); ?></td>
                                    <?php if(config('settings.enable_credits')): ?>
                                        <td><?php echo e($plan->credit_amount); ?></td>
                                    <?php endif; ?>
                                    <td><?php echo e($plan->period == 1 ? __("Monthly") : __("Anually")); ?></td>
                                   
                                    
                                    <td class="text-right">
                                        <div class="d-flex">
                                            <a href="<?php echo e(route('plans.edit', $plan)); ?>" class="btn btn-sm btn-info mr-2"><?php echo e(__('Edit')); ?></a>
                                            <form action="<?php echo e(route('plans.destroy', $plan)); ?>" method="post">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('delete'); ?>
                                                <button type="button" class="btn btn-sm btn-danger" onclick="confirm('<?php echo e(__("Are you sure you want to delete this plan?")); ?>') ? this.parentElement.submit() : ''">
                                                    <?php echo e(__('Delete')); ?>

                                                </button>
                                            </form>
                                        </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                    <div class="card-footer py-4">
                        <?php if(count($plans)): ?>
                            <nav class="d-flex justify-content-end" aria-label="...">
                                <?php echo e($plans->links()); ?>

                            </nav>
                        <?php else: ?>
                            <h4><?php echo e(__('You don`t have any plans')); ?> ...</h4>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', ['title' => __('Pages')], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\zaptra\resources\views/plans/index.blade.php ENDPATH**/ ?>