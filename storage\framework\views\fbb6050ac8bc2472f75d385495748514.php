<?php if(isset($separator)): ?>
    <br />
    <h4 id="sep<?php echo e($id); ?>" class="display-4 mb-0"><?php echo e(__($separator)); ?></h4>
    <hr />
<?php endif; ?>
<div id="form-group-<?php echo e($id); ?>" class="form-group<?php echo e($errors->has($id) ? ' has-danger' : ''); ?>  <?php if(isset($class)): ?> <?php echo e($class); ?> <?php endif; ?>">
    <?php if(!(isset($type)&&$type=="hidden")): ?>
        <label class="form-control-label" for="<?php echo e($id); ?>"><?php echo e(__($name)); ?><?php if(isset($link)): ?><a target="_blank" href="<?php echo e($link); ?>"><?php echo e($linkName); ?></a><?php endif; ?></label>
    <?php endif; ?>
    <div class="input-group">
        <?php if(isset($prepend)): ?>
            <div class="input-group-prepend">
                <span class="input-group-text"><?php echo e($prepend); ?></span>
            </div>
        <?php endif; ?>
        <input   <?php if(isset($changevue)): ?> @change="<?php echo e($changevue); ?>" ref="<?php echo e($id); ?>" <?php endif; ?>        <?php if(isset($onvuechange)): ?> @input="<?php echo e($onvuechange); ?>" ref="<?php echo e($id); ?>" <?php endif; ?>      <?php if(isset($accept)): ?> accept="<?php echo e($accept); ?>" <?php endif; ?> step="<?php echo e(isset($step)?$step:".01"); ?>" <?php if(isset($min)): ?> min="<?php echo e($min); ?>" <?php endif; ?>  <?php if(isset($max)): ?> max="<?php echo e($max); ?>" <?php endif; ?> type="<?php echo e(isset($type)?$type:"color"); ?>" name="<?php echo e($id); ?>" id="<?php echo e($id); ?>" class="form-control form-control <?php if(isset($editclass)): ?> <?php echo e($editclass); ?> <?php endif; ?>  <?php echo e($errors->has($id) ? ' is-invalid' : ''); ?>" placeholder="<?php echo e(__($placeholder)); ?>" value="<?php echo e(old($id)?old($id):(isset($value)?$value:(app('request')->input($id)?app('request')->input($id):null))); ?>" <?php if($required) {echo 'required';} ?> >
    </div> 
    <?php if(isset($additionalInfo)): ?>
        <small class="text-muted"><strong><?php echo e(__($additionalInfo)); ?></strong></small>
    <?php endif; ?>
    <?php if($errors->has($id)): ?>
        <span class="invalid-feedback" role="alert">
            <strong><?php echo e($errors->first($id)); ?></strong>
        </span>
    <?php endif; ?>
</div>
<?php /**PATH C:\xampp\htdocs\zaptra\resources\views/partials/colorpicker.blade.php ENDPATH**/ ?>