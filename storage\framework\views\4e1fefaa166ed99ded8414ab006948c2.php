

<?php $__env->startSection('thead'); ?>
    <th><?php echo e(__('Name')); ?></th>
    <?php if(config('settings.show_company_logo')): ?>
        <th><?php echo e(__('Logo')); ?></th>
    <?php endif; ?>
    <th><?php echo e(__('Owner')); ?></th>
    <th><?php echo e(__('Owner email')); ?></th>
    <th><?php echo e(__('Phone')); ?></th>
    <th><?php echo e(__('Active')); ?></th>
    <th><?php echo e(__('Plan')); ?></th>
    <th><?php echo e(__('crud.actions')); ?></th>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('tbody'); ?>
    <?php $__currentLoopData = $setup['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $company): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <tr>
            <td>
                <?php if(auth()->user()->hasRole('manager')): ?>
                    <a href="<?php echo e(route('admin.companies.loginas', $company)); ?>"><?php echo e($company->name); ?></a>
                <?php else: ?>
                    <a href="<?php echo e(route('admin.companies.edit', $company)); ?>"><?php echo e($company->name); ?></a>
                <?php endif; ?>
            </td>
            
            <?php if(config('settings.show_company_logo')): ?>
                <td><img class="rounded" src=<?php echo e($company->icon); ?> width="50px" height="50px"></img></td>
            <?php endif; ?>
            
            <td><?php echo e($company->user?$company->user->name:__('Deleted')); ?></td>
            <td><a href="mailto: <?php echo e($company->user?$company->user->email:""); ?>"><?php echo e($company->user?$company->user->email:__('Deleted')); ?></a></td>
            <td><a href="tel:<?php echo e($company->phone); ?>"><?php echo e($company->phone); ?></a></td>
            <td>
                <?php if($company->active == 1): ?>
                    <span class="badge badge-success"><?php echo e(__('Active')); ?></span>
                <?php else: ?>
                    <span class="badge badge-warning"><?php echo e(__('Not active')); ?></span>
                <?php endif; ?>
            </td>
            <td><?php echo e(isset($plans) && isset($company->user) && isset($company->user->plan_id) && isset($plans[$company->user->plan_id]) ? $plans[$company->user->plan_id] : ''); ?></td>
            <td>
                <a class="btn btn-sm btn-outline-primary me-2" href="<?php echo e(route('admin.companies.edit', $company)); ?>"><?php echo e(__('Edit')); ?></a>
                <a class="btn btn-sm btn-outline-info me-2" href="<?php echo e(route('admin.companies.loginas', $company)); ?>"><?php echo e(__('Login as')); ?></a>
                <?php if($hasCloner): ?>
                    <a class="btn btn-sm btn-outline-secondary me-2" href="<?php echo e(route('admin.companies.create')."?cloneWith=".$company->id); ?>"><?php echo e(__('Clone it')); ?></a>
                <?php endif; ?>
                <form action="<?php echo e(route('admin.companies.destroy', $company)); ?>" method="post" class="d-inline me-2">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('delete'); ?>
                    <?php if($company->active == 0): ?>
                        <a class="btn btn-sm btn-outline-success" href="<?php echo e(route('admin.company.activate', $company)); ?>"><?php echo e(__('Activate')); ?></a>
                        <a class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this Company from Database? This will aslo delete all data related to it. This is irreversible step.')" href="<?php echo e(route('admin.company.remove',$company)); ?>"><?php echo e(__('Delete')); ?></a>
           
                    <?php else: ?>
                        <button type="button" class="btn btn-sm btn-outline-warning" onclick="confirm('<?php echo e(__("Are you sure you want to deactivate this company?")); ?>') ? this.parentElement.submit() : ''">
                            <?php echo e(__('Deactivate')); ?>

                        </button>
                    <?php endif; ?>
                </form>
             </td>
        </tr>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('general.index', $setup, \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\zaptra\resources\views/companies/index.blade.php ENDPATH**/ ?>