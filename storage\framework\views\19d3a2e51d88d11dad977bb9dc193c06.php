
<?php $__env->startSection('content'); ?>
<div class="header pb-8 pt-2 pt-md-7">
    <div class="container-fluid">
        <div class="header-body">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h2 class="mb-4">🎨 <?php echo e(__('Icon Style')); ?></h2>
                    <div class="card">
                        <div class="card-body">
                            <form method="GET" action="">
                                <div class="form-group">
                                    <label class="form-control-label"><?php echo e(__('Select Icon Type')); ?></label>
                                    <select name="icon_type" class="form-control select2" onchange="this.form.submit()">
                                        <option value="nucleo" <?php echo e(config('settings.icon_type','nucleo') == 'nucleo' ? 'selected' : ''); ?>>
                                            🎯 Nucleo Icons
                                        </option>
                                        <option value="hero" <?php echo e(config('settings.icon_type','nucleo') == 'hero' ? 'selected' : ''); ?>>
                                            ⚡ Hero Icons
                                        </option>
                                    </select>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="header-body mt-5">
            <h1 class="mb-4 mt-3">🎨 <?php echo e(__('Themes')); ?></h1>

            <?php echo $__env->make('partials.flash', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

            <div class="row pt-2">
                <?php $__currentLoopData = $themes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $theme): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-md-4 mb-4">
                    <div class="theme-card card h-100 transition-all hover:shadow-lg">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h3 class="mb-0"><?php echo e($theme['name']); ?></h3>
                            </div>
                            <p class="text-muted mb-4"><?php echo e($theme['description']); ?></p>
                            <div class="theme-preview p-3 rounded mb-4">
                                <div class="color-palette d-flex">
                                    <div class="color-swatch" style="background: <?php echo e($theme['colors']['primary']); ?>"></div>
                                    <div class="color-swatch" style="background: <?php echo e($theme['colors']['secondary']); ?>"></div>
                                    <div class="color-swatch" style="background: <?php echo e($theme['colors']['tertiary']); ?>"></div>
                                </div>
                            </div>
                            <a href="?theme=<?php echo e($theme['file']); ?>" class="btn btn-secondary w-100 theme-button">
                                <i class="fas fa-check me-2"></i><?php echo e(__('Set Theme')); ?>

                            </a>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
</div>

<style>
.theme-card {
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.theme-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary);
}

.color-palette {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.color-swatch {
    width: 33.333%;
    height: 120px;
    border-radius: 0;
    box-shadow: none;
}

.theme-preview {
    background: rgba(0,0,0,0.03);
    border-radius: 15px;
}

.theme-active-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #ddd;
}

.theme-card.active .theme-active-indicator {
    background: var(--primary);
    border-color: var(--primary);
}

.text-gradient {
    background: linear-gradient(45deg, var(--primary), var(--info));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.theme-button {
    transition: all 0.3s ease;
}

.theme-button:hover {
    transform: translateY(-2px);
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', ['title' => __('Themes')], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\zaptra\modules\Themes\Providers/../Resources/views/index.blade.php ENDPATH**/ ?>