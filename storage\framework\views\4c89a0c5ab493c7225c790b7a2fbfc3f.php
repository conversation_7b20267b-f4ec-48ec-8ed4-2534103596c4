
<?php $__env->startSection('thead'); ?>
    <th><?php echo e(__('Title')); ?></th>
    <th><?php echo e(__('Status')); ?></th>
    <th><?php echo e(__('Date')); ?></th>
    <th><?php echo e(__('Actions')); ?></th>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('tbody'); ?>
<?php $__currentLoopData = $setup['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    
    <tr>
        <td style="width: 35%">
            <div class="d-flex align-items-center">
                <?php if($item->featured_image): ?>
                    <img src="<?php echo e($item->featured_image); ?>" class="me-2  mx-2 rounded" style=" height: 40px; object-fit: cover;">
                <?php endif; ?>
                <div>
                    <a href="<?php echo e(route('blogging.edit', ['item' => $item->id])); ?>" class="text-decoration-none">
                        <strong><?php echo e($item->title); ?></strong>
                    </a>
                    <?php if($item->excerpt): ?>
                        <div class="text-muted small"><?php echo e(Str::limit($item->excerpt, 100)); ?></div>
                    <?php endif; ?>
                </div>
            </div>
        </td>
        <td>
            <span class="badge bg-<?php echo e($item->status === 'published' ? 'success' : 'warning'); ?>">
                <?php echo e(ucfirst($item->status)); ?>

            </span>
        </td>

        <td>
            <div><?php echo e($item->created_at->format('M d, Y')); ?></div>
            <small class="text-muted"><?php echo e($item->created_at->format('h:i A')); ?></small>
        </td>
        <td>
            <a href="<?php echo e(route('blogging.edit', ['item' => $item->id])); ?>" class="btn btn-sm btn-primary">
                <?php echo e(__('Edit')); ?>

            </a>
            <a href="<?php echo e(route('blogging.clone', ['item' => $item->id])); ?>" class="btn btn-sm btn-info">
                <?php echo e(__('Clone')); ?>

            </a>
            <a href="<?php echo e(route('blogging.delete', ['item' => $item->id])); ?>" 
               class="btn btn-sm btn-danger" 
               onclick="return confirm('<?php echo e(__('Are you sure you want to delete this post?')); ?>')">
                <?php echo e(__('Delete')); ?>

            </a>
        </td>
    </tr> 
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .table > :not(caption) > * > * {
        padding: 1rem 0.75rem;
        vertical-align: middle;
    }
    .badge {
        padding: 0.5em 0.75em;
        font-weight: 500;
    }
    .btn-group {
        gap: 0.25rem;
    }
    .btn-group .btn {
        border-radius: 0.25rem !important;
    }
    .progress {
        background-color: #e9ecef;
        border-radius: 3px;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('general.index', $setup, \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\zaptra\modules\Blog\Providers/../Resources/views/index.blade.php ENDPATH**/ ?>