<ul class="navbar-nav">
    <li class="nav-item">
        <a class="nav-link <?php if(Route::currentRouteName() == 'dashboard'): ?> active <?php endif; ?>"
            href="<?php echo e(route('dashboard')); ?>">
            <i class="ni ni-tv-2 text-primary"></i> <?php echo e(__('Dashboard')); ?>

        </a>
    </li>
    <?php if(config('settings.admin_companies_enabled',true)): ?>
        <li class="nav-item">
            <a class="nav-link <?php if(Route::currentRouteName() == 'admin.companies.index'): ?> active <?php endif; ?>"
                href="<?php echo e(route('admin.companies.index')); ?>">
                <i class="ni ni-shop text-info"></i> <?php echo e(__('Companies')); ?>

            </a>
        </li> 
    <?php endif; ?>

    <?php echo $__env->make('admin.navbars.menus.extra', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
   

    <li class="nav-item">
        <a class="nav-link <?php if(Route::currentRouteName() == 'admin.landing'): ?> active <?php endif; ?>" href="<?php echo e(route('admin.landing')); ?>">
            <i class="ni ni-html5 text-green"></i> <?php echo e(__('Landing Page')); ?>

        </a>
    </li>
    <?php if(config('settings.pricing_enabled',true)): ?>
        <li class="nav-item ">
            <a class="nav-link <?php if(Route::currentRouteName() == 'plans.index'): ?> active <?php endif; ?>"
                href="<?php echo e(route('plans.index')); ?>">
                <i class="ni ni-credit-card text-orange"></i> <?php echo e(__('Pricing plans')); ?>

            </a>
        </li> 
    <?php endif; ?>

    <!-- if enable credits -->
    <?php if(config('settings.enable_credits')): ?>
        <li class="nav-item">
            <a class="nav-link <?php if(Route::currentRouteName() == 'credits.index'): ?> active <?php endif; ?>" href="<?php echo e(route('credits.index')); ?>">
                <i class="ni ni-credit-card text-blue"></i> <?php echo e(__('Credits')); ?>

            </a>
        </li>
    <?php endif; ?>


    
    <li class="nav-item ">
        <a class="nav-link " target="_blank"
            href="<?php echo e(url('/tools/languages')."/".strtolower(config('settings.app_locale','en'))."/translations"); ?>">
            <i class="ni ni-world text-orange"></i> <?php echo e(__('Translations')); ?>

        </a>
    </li>
    <?php if(!config('settings.hideApps',false)): ?>
        <li class="nav-item">
            <a class="nav-link <?php if(Route::currentRouteName() == 'admin.apps.index'): ?> active <?php endif; ?> " href="<?php echo e(route('admin.apps.index')); ?>">
                <i class="ni ni-spaceship text-red"></i> <?php echo e(__('Apps')); ?>

            </a>
        </li>
    <?php endif; ?>
    <li class="nav-item">
        <a class="nav-link <?php if(Route::currentRouteName() == 'admin.settings.index'): ?> active <?php endif; ?>"
            href="<?php echo e(route('admin.settings.index')); ?>">
            <i class="ni ni-settings text-black"></i> <?php echo e(__('Site Settings')); ?>

        </a>
    </li>





</ul>
<?php /**PATH C:\xampp\htdocs\zaptra\resources\views/admin/navbars/menus/admin.blade.php ENDPATH**/ ?>