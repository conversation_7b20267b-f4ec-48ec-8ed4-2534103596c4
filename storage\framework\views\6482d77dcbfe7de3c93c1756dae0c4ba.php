<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Widget Demo - <?php echo e(config('app.name')); ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .demo-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .feature-list {
            text-align: left;
            max-width: 600px;
            margin: 0 auto;
        }
        .feature-list li {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            list-style: none;
        }
        .feature-list li:before {
            content: "✅ ";
            margin-right: 10px;
        }
        .widget-info {
            background: rgba(34, 197, 94, 0.2);
            border: 2px solid rgba(34, 197, 94, 0.5);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        .code-block {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            text-align: left;
            margin: 20px 0;
            overflow-x: auto;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #22c55e;
            color: white;
            text-decoration: none;
            border-radius: 25px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #16a34a;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 WhatsApp Widget Demo</h1>
        
        <div class="demo-card">
            <h2>Welcome to Zaptra's WhatsApp Widget!</h2>
            <p>Look at the bottom-right corner of your screen - you should see a floating WhatsApp button!</p>
            
            <div class="widget-info">
                <h3>🎯 What You're Seeing</h3>
                <p>The green WhatsApp button floating in the bottom-right corner is your live widget in action!</p>
            </div>
        </div>

        <div class="demo-card">
            <h2>✨ Key Features</h2>
            <ul class="feature-list">
                <li><strong>Instant Contact:</strong> Visitors can reach you immediately via WhatsApp</li>
                <li><strong>Customizable Design:</strong> Colors, text, and branding match your company</li>
                <li><strong>Mobile Responsive:</strong> Works perfectly on all devices</li>
                <li><strong>Easy Integration:</strong> Just copy and paste the embed code</li>
                <li><strong>No Dependencies:</strong> Pure JavaScript, no external libraries needed</li>
                <li><strong>Professional Look:</strong> Clean, modern design that builds trust</li>
            </ul>
        </div>

        <div class="demo-card">
            <h2>🔧 How to Use Your Widget</h2>
            <p>Copy this embed code and paste it into any website:</p>
            
            <?php
                $demoWidget = \Modules\Embedwhatsapp\Models\Whatsappwidget::first();
            ?>
            
            <?php if($demoWidget): ?>
                <div class="code-block">
&lt;script src="<?php echo e(config('app.url')); ?>/popup/whatsapp?id=<?php echo e($demoWidget->id); ?>"&gt;&lt;/script&gt;<br>
&lt;div id="embed-whatsapp-chat"&gt;&lt;/div&gt;
                </div>
            <?php else: ?>
                <div class="code-block">
                    No widget found. Please create a widget first in your dashboard.
                </div>
            <?php endif; ?>
        </div>

        <div class="demo-card">
            <h2>🎨 Try It Now!</h2>
            <p>Click the WhatsApp button in the bottom-right corner to see how it works!</p>
            <p>It will open WhatsApp with a pre-filled message to your configured phone number.</p>
            
            <a href="<?php echo e(route('embedwhatsapp.edit')); ?>" class="btn">
                🛠️ Customize Your Widget
            </a>
            
            <a href="<?php echo e(url('/')); ?>" class="btn">
                🏠 Back to Home
            </a>
        </div>

        <div class="demo-card">
            <h2>💡 Business Benefits</h2>
            <ul class="feature-list">
                <li><strong>Higher Conversion:</strong> Instant communication increases sales</li>
                <li><strong>Better Support:</strong> Customers get immediate help</li>
                <li><strong>Global Reach:</strong> WhatsApp is used worldwide</li>
                <li><strong>Trust Building:</strong> Direct communication builds relationships</li>
                <li><strong>Lead Generation:</strong> Capture more potential customers</li>
                <li><strong>Cost Effective:</strong> Free communication channel</li>
            </ul>
        </div>
    </div>

    <!-- WhatsApp Widget -->
    <?php if($demoWidget): ?>
        <script src="<?php echo e(config('app.url')); ?>/popup/whatsapp?id=<?php echo e($demoWidget->id); ?>"></script>
        <div id="embed-whatsapp-chat"></div>
    <?php endif; ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\zaptra\resources\views/whatsapp-widget-demo.blade.php ENDPATH**/ ?>